
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { ExternalLink, Mail, Github, Twitter } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-black border-t border-gray-800">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="flex items-center space-x-2 mb-4"
            >
              <img
                src="/metamorphic-labs-logo.png"
                alt="Metamorphic Labs"
                className="w-8 h-8 object-contain"
              />
              <span className="font-semibold text-lg text-white">Metamorphic Labs</span>
            </motion.div>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-gray-300 max-w-md leading-relaxed"
            >
              Redefining Reality with AI, Quantum Systems & Intelligent Software.
              We pioneer next-generation AI systems, multi-agent orchestration,
              and boundary-pushing software solutions.
            </motion.p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h3 className="font-semibold mb-4 text-white">Navigation</h3>
            <ul className="space-y-2">
              <li><Link to="/about" className="text-gray-300 hover:text-white hover:text-gradient transition-all duration-300">About</Link></li>
              <li><Link to="/systems" className="text-gray-300 hover:text-white hover:text-gradient transition-all duration-300">Systems</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-white hover:text-gradient transition-all duration-300">Contact</Link></li>
            </ul>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h3 className="font-semibold mb-4 text-white">Our Systems</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="https://catalyst.metamorphiclabs.ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white hover:text-gradient transition-all duration-300 inline-flex items-center"
                >
                  Catalyst <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </li>
              <li><span className="text-gray-300">Metamorphic Reactor</span></li>
              <li>
                <a
                  href="https://vault024.metamorphiclabs.ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gold hover:text-gold/80 transition-all duration-300 inline-flex items-center"
                >
                  Vault 024 <ExternalLink className="ml-1 h-3 w-3" />
                </a>
              </li>
            </ul>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-8 pt-8 border-t border-gray-800"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-center md:text-left text-gray-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} Metamorphic Labs LLC. All rights reserved.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-white transition-colors duration-300"
                aria-label="Email"
              >
                <Mail className="h-5 w-5" />
              </a>
              <a
                href="https://github.com/metamorphic-labs"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors duration-300"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com/metamorphiclabs"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors duration-300"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
